# run_single_test_playwright.py
# V2.2 - 最终修正版：通过创建新页面解决导航失败问题

import time
import os
import asyncio
from playwright.async_api import async_playwright, Page, BrowserContext, Browser

# 从我们自己创建的模块中导入函数
from bit_api_controller import open_browser, close_browser
from xiaohongshu_playwright_automator import publish_note_playwright

# --- 测试参数 ---
TEST_PROFILE_ID = "2ad76161e6514ccfad82edaa99719349" 

TEST_NOTE_DATA = {
    # ... 你的测试数据保持不变 ...
    "目标账号昵称": "我的Playwright最终测试号",
    "比特窗口": TEST_PROFILE_ID,
    "标题": "【V2.2最终测试】釜底抽薪，掌控一切！",
    "内容": "这次，我们不再使用默认页面，而是创建自己的“领地”来执行任务！",
    "内容话题": "#Playwright #自动化 #最终决战 #Python",
    "文件路径": "D:/bitbrowser-auto/images",
    "文件名称": "test_cover.jpg",
    "类型": "图文"
}
# -----------------------------------------

async def main_test_run():
    """主测试运行函数"""
    print(f"--- 启动【V2.2 - 创建新页面】测试程序 ---")
    
    browser_data = open_browser(TEST_PROFILE_ID)
    if not browser_data:
        return

    browser: Browser | None = None
    try:
        cdp_endpoint = f"http://{browser_data.get('http')}"
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(cdp_endpoint)
            
            if not browser.contexts:
                print("❌ 错误：未能找到浏览器上下文(Context)。")
                return
            context: BrowserContext = browser.contexts[0]
            
            # --- 【核心修正：创建并使用新页面】 ---
            print("  - 正在新建一个干净的页面(Tab)...")
            page: Page = await context.new_page()
            print("  - ✅ 新页面已创建，并准备好接收指令！")
            
            # (可选) 关闭比特浏览器可能默认打开的其他页面，保持环境干净
            if len(context.pages) > 1:
                # pages[0]是旧的，pages[1]是我们新建的
                # 但为了保险，我们只保留最新的页面
                for p in context.pages[:-1]:
                    await p.close()
            # ------------------------------------

            # 步骤3：调用核心发布函数，传入我们自己创建的这个干净的page对象
            status = await publish_note_playwright(page, TEST_NOTE_DATA)
            
            print(f"\n--- 发布函数执行完毕，返回状态: {status} ---")

    except Exception as e:
        print(f"❌ 在主调度程序中发生严重错误: {e}")
    
    finally:
        print("\n测试结束，窗口将保持打开20秒供你检查，然后自动关闭...")
        await asyncio.sleep(20)
        if browser:
            await browser.close()
        
        if TEST_PROFILE_ID:
            close_browser(TEST_PROFILE_ID)
            print("比特浏览器关闭指令已发送。")

if __name__ == "__main__":
    asyncio.run(main_test_run())