# bit_api_controller.py
# 职责：封装所有与比特浏览器本地API的直接交互，提供清晰、简单的函数供主程序调用。

import requests
import json
import time

# 从我们的配置文件中导入配置信息
from config import BIT_API_BASE_URL, JSON_HEADERS

def open_browser(profile_id: str) -> dict | None:
    """
    【核心功能】根据配置文件ID，通过API打开一个浏览器窗口。

    Args:
        profile_id (str): 你在比特浏览器中设置的窗口ID（或称为“比特窗口”ID）。

    Returns:
        dict | None: 如果成功，返回包含WebDriver信息的data对象；如果失败，返回None。
    """
    api_endpoint = f"{BIT_API_BASE_URL}/browser/open"
    payload = {"id": profile_id}
    
    print(f"▶️  正在向API发送指令: 启动窗口 {profile_id} ...")
    
    try:
        response = requests.post(api_endpoint, data=json.dumps(payload), headers=JSON_HEADERS)
        response.raise_for_status()  # 检查HTTP请求是否成功 (如404, 500等)
        response_data = response.json()

        if response_data.get("success"):
            print(f"✅ 窗口 {profile_id} 启动成功！")
            return response_data.get("data")
        else:
            # API返回了成功状态码，但业务逻辑失败
            print(f"❌ 窗口 {profile_id} 启动失败，API返回信息: {response_data.get('msg', '未知业务错误')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败，请检查比特浏览器软件是否已打开，以及API地址是否正确。错误: {e}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 解析API返回的JSON数据失败，返回内容: {response.text}")
        return None

def close_browser(profile_id: str) -> bool:
    """
    【核心功能】根据ID关闭指定的浏览器窗口。

    Args:
        profile_id (str): 你要关闭的窗口ID。

    Returns:
        bool: 操作是否成功发出。
    """
    api_endpoint = f"{BIT_API_BASE_URL}/browser/close"
    payload = {'id': profile_id}
    
    print(f"▶️  正在向API发送指令: 关闭窗口 {profile_id} ...")
    
    try:
        response = requests.post(api_endpoint, data=json.dumps(payload), headers=JSON_HEADERS)
        response.raise_for_status()
        response_data = response.json()
        
        if response_data.get("success"):
            print(f"✅ 窗口 {profile_id} 关闭指令已发送。")
            return True
        else:
            print(f"❌ 窗口 {profile_id} 关闭失败: {response_data.get('msg', '未知业务错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭窗口API请求失败: {e}")
        return False