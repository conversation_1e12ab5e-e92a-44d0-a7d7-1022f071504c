# main.py
# V1.3 - 修复了所有已知的“缺少import”错误

import time
import pandas as pd
import os
import asyncio
import random
from datetime import date
# --- 【核心修正】从playwright.async_api中导入async_playwright ---
from playwright.async_api import async_playwright
# -----------------------------------------------------------

# 从我们自己创建的模块中导入所有需要的函数
from bit_api_controller import open_browser, close_browser
from xiaohongshu_playwright_automator import publish_note_playwright


def load_tasks_from_excel(file_path: str) -> list:
    """从Excel文件中加载当日的任务清单"""
    try:
        if not os.path.exists(file_path):
            print(f"❌ 任务文件不存在: {file_path}")
            return []
        df = pd.read_excel(file_path, dtype=str).fillna('')
        return df.to_dict('records')
    except Exception as e:
        print(f"❌ 读取Excel文件时出错: {e}")
        return []


async def run_batch_publish():
    """主程序：批量发布流程"""
    # 1. 确定当日的发布清单文件名
    today_str = date.today().strftime("%Y-%m-%d")
    daily_plan_file = f"{today_str}_发布清单.xlsx"

    print(f"--- 启动【主发布程序 V1.3】，准备执行任务文件: {daily_plan_file} ---")

    # 2. 加载今天的任务列表
    tasks = load_tasks_from_excel(daily_plan_file)
    if not tasks:
        print("--- 今天没有发布任务，程序结束。 ---")
        return

    print(f"--- 共发现 {len(tasks)} 条待发布任务 ---")

    results_df = pd.DataFrame(tasks)

    # 3. 遍历并执行每个任务
    for index, task_data in results_df.iterrows():
        print(f"\n--- [ {index + 1}/{len(tasks)} ] 开始处理任务：账号 {task_data.get('比特窗口')} ---")
        profile_id = str(task_data.get('比特窗口', '')).strip()

        if not profile_id:
            print("  - ❌ 错误：表格中'比特窗口'ID为空，跳过此任务。")
            results_df.loc[index, '运行状态'] = "失败：缺少比特窗口ID"
            continue

        browser_data = open_browser(profile_id)
        if not browser_data:
            results_df.loc[index, '运行状态'] = "失败：窗口启动失败"
            continue

        browser = None
        try:
            cdp_endpoint = f"http://{browser_data.get('http')}"
            async with async_playwright() as p:
                browser = await p.chromium.connect_over_cdp(cdp_endpoint)
                context = browser.contexts[0]
                page = await context.new_page()
                print("  - ✅ Playwright已成功接管。")

                status = await publish_note_playwright(page, task_data)

                results_df.loc[index, '运行状态'] = status

        except Exception as e:
            print(f"❌ 处理任务时发生严重错误: {e}")
            results_df.loc[index, '运行状态'] = f"严重错误: {e}"

        finally:
            if browser and browser.is_connected():
                await browser.close()

            if profile_id:
                close_browser(profile_id)
            print("  - 浏览器已关闭。")

        if index < len(tasks) - 1:
            sleep_time = random.randint(60, 180)
            print(f"--- 本条任务完成，等待 {sleep_time} 秒后继续 ---")
            time.sleep(sleep_time)

    # 4. 保存结果
    result_file_name = f"{today_str}_发布结果.xlsx"
    results_df.to_excel(result_file_name, index=False)
    print(f"\n--- 所有任务处理完毕！结果已保存至: {result_file_name} ---")


if __name__ == "__main__":
    asyncio.run(run_batch_publish())
