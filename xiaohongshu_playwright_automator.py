import time
import os
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

async def publish_note_playwright(page: Page, note_data: dict) -> str:
    """
    使用Playwright自动化在小红书上发布图文笔记。
    V3.0: 修复了点击发布按钮时的等待逻辑错误。
    """
    print("▶️ 开始执行【Playwright】小红书发布流程...")
    try:
        # ... [步骤 1 到 5 的代码保持不变，这里省略] ...
        # 1. 导航并等待
        publish_url = "https://creator.xiaohongshu.com/publish/publish"
        print(f"  - 步骤1: 正在导航到发布页面: {publish_url}")
        await page.goto(publish_url, wait_until="networkidle", timeout=30000)
        await page.set_viewport_size({"width": 1920, "height": 1080})
        print("  - 页面视窗已设置，等待稳定...")
        await page.wait_for_timeout(3000)

        # 2. 点击“上传图文”Tab
        print("  - 步骤2: 正在定位'上传图文'Tab...")
        tab_locator = page.locator("div.creator-tab:has-text('上传图文')").first
        try:
            await tab_locator.evaluate("element => element.click()")
            print("  - ✅ JS点击指令已发送！")
        except Exception:
            print(f"  - ❌ JS点击失败，尝试坐标点击...")
            box = await tab_locator.bounding_box()
            if box:
                await page.mouse.click(box['x'] + box['width'] / 2, box['y'] + box['height'] / 2)
                print("  - ✅ 物理鼠标坐标点击指令已发送！")
            else:
                raise Exception("无法获取'上传图文'Tab的边界框。")
        
        await page.wait_for_timeout(3000)
        print("  - Tab切换步骤已执行。")

        # 3. 上传封面图
        print("  - 步骤3: 正在等待并上传封面...")
        file_input_selector = 'input[type="file"]'
        await page.locator(file_input_selector).first.set_input_files(
            os.path.join(note_data['文件路径'], note_data['文件名称'])
        )
        print("  - ✅ 封面图已上传成功！")
        await page.wait_for_timeout(2000) 

        # 4. 填写标题
        print("  - 步骤4: 正在填写标题...")
        title_selector = 'input[placeholder*="填写标题"]'
        title_locator = page.locator(title_selector)
        await title_locator.wait_for(state="visible", timeout=15000)
        await title_locator.fill(note_data['标题'])
        print(f"  - ✅ 标题 '{note_data['标题']}' 已成功填写！")

        # 5. 填写正文
        print("  - 步骤5: 正在填写正文...")
        editor_selector = "#quillEditor > div.ql-editor"
        editor_locator = page.locator(editor_selector)
        await editor_locator.wait_for(state="visible", timeout=15000)
        await editor_locator.click()
        await page.wait_for_timeout(500)
        full_content = f"{note_data['内容']}\n\n{note_data['内容话题']}"
        await editor_locator.fill(full_content)
        print(f"  - ✅ 正文和话题已成功填写！")
        
        # --- 【修正部分】步骤6: 点击发布按钮 ---
        print("  - 步骤6: 正在点击发布按钮...")
        
        publish_button_selector = 'div.submit button:has-text("发布")'
        publish_button_locator = page.locator(publish_button_selector)

        # 【修正】: 直接点击！ Playwright的.click()会自动等待按钮变为enabled状态
        # 我们不再需要那行错误的 wait_for 代码。
        # .click() 会在20秒内持续尝试，直到按钮可被点击。
        await publish_button_locator.click(timeout=20000) 
        
        print("  - ✅ 发布按钮已点击！")
        # --- 修正完毕 ---

        # --- 步骤7: 验证发布成功 ---
        print("  - 步骤7: 等待发布成功确认信息...")
        success_locator = page.locator("text='发布成功'")
        await success_locator.wait_for(state="visible", timeout=30000)
        print("  - ✅ 检测到'发布成功'提示！笔记已成功发布！")

        print(f"\n🎉🎉🎉 恭喜！笔记 '{note_data['标题']}' 已全部自动化发布成功！")
        return "发布成功"

    except Exception as e:
        error_msg = f"❌ 发布失败：发生错误: {e}"
        screenshot_path = f"playwright_error_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"{error_msg} 截图已保存至 {screenshot_path}。")
        return error_msg